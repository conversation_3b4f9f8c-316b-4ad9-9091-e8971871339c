# scenario/urls.py
from django.urls import path
from . import views
urlpatterns = [
   path('create-scenario/', views.ScenarioCreateAPIView.as_view(), name='create-scenario'),
   path('clusters/', views.ClusterByLocationView.as_view(), name='get-cluster-data'),
   path('scenario_list/', views.ScenarioList.as_view(), name='ScenarioList'),
   path('select_store_dropdown/', views.StoreSelectionDropdown.as_view(), name='select_store_dropdown'),
   path('outlier/', views.OutlierAPIView.as_view(), name='outlier-api'),
   path('testAndControlStore/', views.testAndControlStore.as_view(), name='testAndControlStore'),
   path('runOutliers/', views.RunOutliers.as_view(), name='runOtliers'),
   path('insertTestControlStr/', views.insertTestControlStr.as_view(), name='insertTestControlStr'),
   path('getDataSummary/', views.getDataSummary.as_view(), name='getDataSummary'),
   path('getMetricGraph/', views.getMetricGraph.as_view(), name='getMetricGraph'),
   path('getAllGDCSdata/', views.getAllGDCSdata.as_view(), name='getAllGDCSdata'),
   path('health-metrics/', views.HealthMetricsView.as_view(), name='app-hb-preopt'),
   path('upload/', views.FileUploadAPIView.as_view(), name='upload'),
   path('getOptimizerDetails/', views.getOptimizerDetails.as_view(), name='getOptimizerDetails'),
   
]
