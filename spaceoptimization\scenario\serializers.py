from rest_framework import serializers
from .models import ScenarioMetad
import json


class ScenarioMetadSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScenarioMetad
        fields = '__all__'


class ClusterRequestSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    # loc_codes = serializers.ListField(
    #     child=serializers.CharField(), allow_empty=False
    # )
class StoreSelectionDropdownSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()

class ClusterDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None  # will be set dynamically
        fields = [
            "loc_cd", "loc_nm", "rgn_nm", "revenue", "units", "gmv",
            "total_lm", "total_customer", "total_invoice", "area_sqft",
            "cluster_num", "volume_contribution", "ethnicity_contribution",
            "revenue_per_sqft"
        ]

class ClusterUpdateSerializer(serializers.Serializer):
    loc_cd = serializers.IntegerField()
    cluster_num = serializers.IntegerField()
    concept = serializers.CharField(required=False)

class StoreSelectionDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None
        fields = ['loc_cd', 'loc_nm']


class OutlierUpdateSerializer(serializers.Serializer):
    sub_clss_nm = serializers.CharField(max_length=100, required=True)
    loc_cd = serializers.CharField(max_length=20, required=True)
    outlier_status = serializers.CharField(max_length=20, required=True)
    outlier_status_final = serializers.CharField(max_length=20, required=True)
    month = serializers.IntegerField(required=True)

class testAndControlStoreSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    
class OutlierDetectionRequestSerializer(serializers.Serializer):
    scenario_id = serializers.IntegerField(required=True)
    concept = serializers.CharField(max_length=50, required=True)
    territory = serializers.CharField(max_length=50, required=True)
    user_id = serializers.IntegerField(required=True)

class OutlierDetectionResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    processed_stores = serializers.IntegerField()
    outliers_found = serializers.IntegerField()
    major_outliers = serializers.IntegerField()
    minor_outliers = serializers.IntegerField()
    execution_time = serializers.FloatField()
    
class InsertTestControlStrRequestSerializer(serializers.Serializer):
    scenario_id = serializers.IntegerField(required=True)
    store_codes = serializers.CharField(max_length=256, required=True)

class InsertTestControlStrResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    scenario_id = serializers.IntegerField()
    updated_stores = serializers.IntegerField()
    execution_time = serializers.FloatField()

class GraphDataRequestSerializer(serializers.Serializer):
    concept = serializers.CharField(max_length=64, required=True)
    # scenario_id = serializers.IntegerField(required=True)
    group = serializers.ListField(child=serializers.CharField(), required=False, default=[])
    department = serializers.ListField(child=serializers.CharField(), required=False, default=[])
    class_field = serializers.ListField(child=serializers.CharField(), required=False, default=[], source='class')
    sub_class = serializers.ListField(child=serializers.CharField(), required=False, default=[])
    from_month = serializers.CharField(required=True)  # Format: YYYY-MM
    to_month = serializers.CharField(required=True)    # Format: YYYY-MM
    

class GraphDataPointSerializer(serializers.Serializer):
    month = serializers.CharField()
    total_lm = serializers.FloatField()
    productivity = serializers.FloatField()
    gmv_per_lm = serializers.FloatField()
    group_name = serializers.CharField(required=False)
    department_name = serializers.CharField(required=False)
    class_name = serializers.CharField(required=False)
    sub_class_name = serializers.CharField(required=False)

class GraphDataResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    data_points = GraphDataPointSerializer(many=True)
    total_records = serializers.IntegerField()
    aggregation_level = serializers.CharField()
    filters_applied = serializers.DictField()
    
class MetricGraphRequestSerializer(serializers.Serializer):
    concept = serializers.CharField(max_length=64, required=True)
    scenario_id = serializers.IntegerField(required=True)
    # loc_cd = serializers.CharField(max_length=20, required=True)
    metric = serializers.CharField(max_length=50, required=True)


class SubclassDataSerializer(serializers.Serializer):
    sub_class_name = serializers.CharField()
    metric_value = serializers.FloatField()
    rank = serializers.IntegerField()
    category = serializers.CharField()  # 'top' or 'bottom'

class MetricGraphResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    store_code = serializers.CharField()
    store_name = serializers.CharField()
    latest_month = serializers.CharField()
    metric = serializers.CharField()
    metric_display_name = serializers.CharField()
    top_5 = SubclassDataSerializer(many=True)
    bottom_5 = SubclassDataSerializer(many=True)
    total_subclasses = serializers.IntegerField()
    
class GDCSDataRequestSerializer(serializers.Serializer):
    """Serializer for request validation"""
    concept = serializers.CharField(max_length=100, required=True)
    # scenario_id = serializers.IntegerField(required=True, min_value=1)
