from django.db import models

class FileUpload(models.Model):
    id = models.AutoField(primary_key=True)   # or whatever is the PK in `file_uploads`
    file_name = models.CharField(max_length=128)
    file_type = models.CharField(max_length=32)
    uploaded_at = models.DateTimeField()
    url = models.CharField(max_length=256)

    class Meta:
        db_table = 'file_uploads'
        managed = False  

class ScenarioMetad(models.Model):
    name = models.CharField(max_length=128)
    user_id = models.IntegerField(null=True, blank=True) #remove null after users table is added
    season_type = models.CharField(max_length=32)
    eval_type = models.CharField(max_length=32,blank=True,null=True)
    event_name = models.CharField(max_length=128)
    eval_start = models.DateTimeField()
    eval_end = models.DateTimeField()
    ref_start = models.DateField(max_length=128)
    ref_end = models.DateField(max_length=128)
    CNCPT_NM = models.CharField(max_length=64)
    TERRITORY_NM = models.CharField(max_length=64)
    metric = models.CharField(max_length=64)
    sqft_file = models.ForeignKey(FileUpload, null=True, blank=True, on_delete=models.SET_NULL, related_name='sqft_files')
    mdq_file = models.ForeignKey(FileUpload, null=True, blank=True, on_delete=models.SET_NULL, related_name='mdq_files')
    cover_file = models.ForeignKey(FileUpload, null=True, blank=True, on_delete=models.SET_NULL, related_name='cover_files')
    exclusion_file_id = models.IntegerField(null=True, blank=True)
    loc_cd = models.CharField(max_length=256)
    created_by = models.IntegerField(null=True, blank=True) #remove null after users table is added
    updated_by = models.IntegerField(null=True, blank=True) #remove null after users table is added
    created_at = models.DateTimeField(blank=True,null=True)
    updated_at = models.DateTimeField(blank=True,null=True)

    class Meta:
        managed = False
        db_table = 'SCENARIO_METAD'




class ScenarioStatus(models.Model):
    scenario = models.ForeignKey(ScenarioMetad, on_delete=models.CASCADE, db_column='scenario_id')
    status = models.CharField(max_length=64, default='CREATED')

    class Meta:
        managed = False
        db_table = 'SCENARIO_STATUS'


class BaseDeClusterModel(models.Model):
    loc_cd = models.CharField(max_length=20)
    loc_nm = models.CharField(max_length=100)
    rgn_nm = models.CharField(max_length=100)
    revenue = models.DecimalField(max_digits=32, decimal_places=1)
    units = models.DecimalField(max_digits=32, decimal_places=1)
    gmv = models.DecimalField(max_digits=32, decimal_places=1, db_column='GMV')
    total_lm = models.DecimalField(max_digits=32, decimal_places=1)
    total_customer = models.BigIntegerField()
    total_invoice = models.BigIntegerField()
    area_sqft = models.DecimalField(max_digits=32, decimal_places=1)
    cluster_num = models.IntegerField()
    volume_contribution = models.JSONField()
    ethnicity_contribution = models.JSONField()
    revenue_per_sqft = models.DecimalField(max_digits=32, decimal_places=1)
    last_update_dt_tm = models.DateTimeField()
    similarity_scores = models.JSONField(null=True, blank=True)

    class Meta:
        abstract = True


# Concept: LS
class DeLsAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ae_cluster'

class DeLsBhCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_bh_cluster'

class DeLsEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_eg_cluster'

class DeLsKsCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ks_cluster'

class DeLsKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_kw_cluster'

class DeLsOmCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_om_cluster'

class DeLsQtCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_qt_cluster'

class DeLsLbCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_lb_cluster'

class DeLsJdCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_jd_cluster'


# Concept: HB
class DeHbAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ae_cluster'

class DeHbBhCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_bh_cluster'

class DeHbEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_eg_cluster'

class DeHbKsCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ks_cluster'

class DeHbKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_kw_cluster'

class DeHbOmCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_om_cluster'

class DeHbQtCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_qt_cluster'

class DeHbLbCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_lb_cluster'

class DeHbJdCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_jd_cluster'


# Concept: SP
class DeSpAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ae_cluster'

class DeSpBhCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_bh_cluster'

class DeSpEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_eg_cluster'

class DeSpKsCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ks_cluster'

class DeSpKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_kw_cluster'

class DeSpOmCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_om_cluster'

class DeSpQtCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_qt_cluster'

class DeSpLbCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_lb_cluster'

class DeSpJdCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_jd_cluster'


class BaseAppClusterModel(models.Model):
    scenario_id = models.IntegerField()
    territory_nm = models.CharField(max_length=64)
    loc_cd = models.CharField(max_length=32)
    loc_nm = models.CharField(max_length=128)
    stnd_trrtry_nm = models.CharField(max_length=64)
    rgn_nm = models.CharField(max_length=64)

    revenue = models.DecimalField(max_digits=32, decimal_places=1)
    units = models.DecimalField(max_digits=32, decimal_places=1)
    gmv = models.DecimalField(max_digits=32, decimal_places=1)
    total_lm = models.DecimalField(max_digits=32, decimal_places=1)
    total_customer = models.BigIntegerField()
    total_invoice = models.BigIntegerField()
    area_sqft = models.DecimalField(max_digits=32, decimal_places=1)

    cluster_num = models.IntegerField()
    volume_contribution = models.JSONField()
    ethnicity_contribution = models.JSONField()
    revenue_per_sqft = models.DecimalField(max_digits=32, decimal_places=1)

    updated_at = models.DateTimeField()
    updated_by = models.IntegerField()
    new_cluster_num = models.IntegerField()

    class Meta:
        abstract = True


class AppLsStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_ls_str_cluster'


class AppHbStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_hb_str_cluster'


class AppSpStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_sp_str_cluster'


class BaseDePreoptModel(models.Model):
    loc_cd = models.IntegerField()
    loc_nm = models.CharField(max_length=100)
    rgn_nm = models.CharField(max_length=100)
    grp_nm = models.CharField(max_length=100)
    dpt_nm = models.CharField(max_length=100)
    clss_nm = models.CharField(max_length=100)
    sub_clss_nm = models.CharField(max_length=100)
    month = models.IntegerField(db_column='MONTH') 
    mnth_avg_soh = models.FloatField()
    mnth_avg_itm_cnt = models.FloatField()
    mnth_avg_optn_cnt = models.FloatField()
    mnth_end_soh = models.FloatField()
    mnth_end_itm_cnt = models.IntegerField()
    mnth_end_optn_cnt = models.IntegerField()
    net_sls_amt = models.FloatField()
    rtl_qty = models.FloatField()
    gmv = models.FloatField(db_column='gmv')
    inv_cnt = models.IntegerField()
    cust_cnt = models.IntegerField()
    str_visits = models.IntegerField()
    str_cust_cnt = models.IntegerField()
    cust_pen = models.FloatField()
    spc = models.FloatField()
    margin_perc = models.FloatField()
    asp = models.FloatField()
    sls_per_inv = models.FloatField()
    units_per_inv = models.FloatField()
    ros = models.FloatField()
    cover = models.FloatField()
    total_lm = models.FloatField()
    gmv_per_day = models.FloatField()
    gmv_per_lm = models.FloatField()
    lm_contribution_in_store = models.FloatField()
    outlier_status = models.CharField(max_length=20)
    suggested_total_lm = models.FloatField()
    last_update_dt_tm = models.DateTimeField()

    class Meta:
        abstract = True
# Concept: LS
class DeLsAePreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ae_preopt'

class DeLsBhPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_bh_preopt'

class DeLsEgPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_eg_preopt'

class DeLsKsPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ks_preopt'

class DeLsKwPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_kw_preopt'

class DeLsOmPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_om_preopt'

class DeLsQtPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_qt_preopt'

class DeLsLbPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_lb_preopt'

class DeLsJdPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_jd_preopt'


# Concept: HB
class DeHbAePreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ae_preopt'

class DeHbBhPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_bh_preopt'

class DeHbEgPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_eg_preopt'

class DeHbKsPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ks_preopt'

class DeHbKwPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_kw_preopt'

class DeHbOmPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_om_preopt'

class DeHbQtPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_qt_preopt'

class DeHbLbPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_lb_preopt'

class DeHbJdPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_jd_preopt'


# Concept: SP
class DeSpAePreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ae_preopt'

class DeSpBhPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_bh_preopt'

class DeSpEgPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_eg_preopt'

class DeSpKsPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ks_preopt'

class DeSpKwPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_kw_preopt'

class DeSpOmPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_om_preopt'

class DeSpQtPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_qt_preopt'

class DeSpLbPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_lb_preopt'

class DeSpJdPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_jd_preopt'

class BaseAppPreoptModel(models.Model):
    territory_nm = models.CharField(max_length=20)
    loc_cd = models.IntegerField()
    loc_nm = models.CharField(max_length=100)
    rgn_nm = models.CharField(max_length=100)
    grp_nm = models.CharField(max_length=100)
    dpt_nm = models.CharField(max_length=100)
    clss_nm = models.CharField(max_length=100)
    sub_clss_nm = models.CharField(max_length=100)
    month = models.IntegerField(db_column='MONTH') 
    mnth_avg_soh = models.FloatField()
    mnth_avg_itm_cnt = models.FloatField()
    mnth_avg_optn_cnt = models.FloatField()
    mnth_end_soh = models.FloatField()
    mnth_end_itm_cnt = models.IntegerField()
    mnth_end_optn_cnt = models.IntegerField()

    net_sls_amt = models.FloatField()
    rtl_qty = models.FloatField()
    gmv = models.FloatField(db_column='GMV')
    inv_cnt = models.IntegerField()
    cust_cnt = models.IntegerField()
    str_visits = models.IntegerField()
    str_cust_cnt = models.IntegerField()

    cust_pen = models.FloatField()
    spc = models.FloatField()
    margin_perc = models.FloatField()
    asp = models.FloatField()
    sls_per_inv = models.FloatField()
    units_per_inv = models.FloatField()
    ros = models.FloatField()
    cover = models.FloatField()

    total_lm = models.FloatField()
    gmv_per_day = models.FloatField()
    gmv_per_lm = models.FloatField()
    lm_contribution_in_store = models.FloatField()

    outlier_status = models.CharField(max_length=20)
    suggested_total_lm = models.FloatField()
    last_update_dt_tm = models.DateTimeField()
    outlier_status_final = models.CharField(max_length=20, null=True, blank=True)
    # scenario_id = models.IntegerField()

    class Meta:
        abstract = True

class AppLsPreopt(BaseAppPreoptModel):
    class Meta:
        managed = False
        db_table = 'app_ls_preopt'


class AppHbPreopt(BaseAppPreoptModel):
    class Meta:
        managed = False
        db_table = 'app_hb_preopt'


class AppSpPreopt(BaseAppPreoptModel):
    class Meta:
        managed = False
        db_table = 'app_sp_preopt'


class FileUploads(models.Model):
    file_name = models.CharField(max_length=128, null=True, blank=True)
    file_type = models.CharField(max_length=32, null=True, blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    url = models.CharField(max_length=256, null=True, blank=True)

    def __str__(self):
        return self.file_name or f"FileUploads {self.id}"
    class Meta:
        managed = False
        db_table = 'file_uploads'

class BaseScenarioFileData (models.Model):
    scenario = models.ForeignKey("ScenarioMetad", on_delete=models.CASCADE,null=True, blank=True)
    file_upload = models.ForeignKey("FileUploads", on_delete=models.CASCADE)
    
    territory_nm = models.CharField(max_length=100, null=True, blank=True)
    loc_cd = models.CharField(max_length=100, null=True, blank=True)
    grp_nm = models.CharField(max_length=100, null=True, blank=True)
    dpt_nm = models.CharField(max_length=100, null=True, blank=True)
    clss_nm = models.CharField(max_length=100, null=True, blank=True)
    sub_clss_nm = models.CharField(max_length=100, null=True, blank=True)

    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True

class SqftFileData(BaseScenarioFileData ):
    sqft = models.FloatField(null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'sqft_filedata'

class MdqFileData(BaseScenarioFileData ):
    mdq = models.FloatField(null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'mdq_filedata'


class CoverFileData(BaseScenarioFileData ):
    mid_cover_start = models.FloatField(null=True, blank=True)
    high_cover_start = models.FloatField(null=True, blank=True)
    depth = models.FloatField(null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'cover_filedata'

class ExclusionFileData(BaseScenarioFileData ):
    item_cd = models.IntegerField(null=True,blank=True)
    class Meta:
        managed = False
        db_table = 'exclusion_filedata'


