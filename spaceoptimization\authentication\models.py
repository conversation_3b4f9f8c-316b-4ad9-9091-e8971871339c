from django.db import models

class UserConceptMapping(models.Model):
    id = models.BigAutoField(primary_key=True)  # Auto increment BIGINT
    user_id = models.IntegerField()             # USER_ID as int
    concept_nm = models.CharField(max_length=255)  # CONCEPT_NM as varchar(255)

    class Meta:
        db_table = "user_concept_mapping"

from django.db import models


class Role(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=32, null=True, blank=True)

    class Meta:
        db_table = "role"


class Users(models.Model):
    id = models.AutoField(primary_key=True)
    first_name = models.Char<PERSON>ield(max_length=64, null=True, blank=True)
    last_name = models.Char<PERSON>ield(max_length=64, null=True, blank=True)
    email = models.CharField(max_length=64, null=True, blank=True)
    role = models.ForeignKey(
        Role, on_delete=models.SET_NULL, null=True, blank=True, db_column="role_id"
    )
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "users"

