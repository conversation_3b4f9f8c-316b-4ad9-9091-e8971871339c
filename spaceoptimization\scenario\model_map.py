from .models import (
    AppHbPreopt,
    DeHbAeCluster,
    DeLsKwCluster,
    DeSpEgCluster,
    AppLsStrCluster,
    AppHbStrCluster,
    AppSpStrCluster,
    DeHbAeCluster, DeHbBhCluster, DeHbEgCluster,
    DeHbKsCluster, DeHbKwCluster, DeHbOmCluster, DeHbQtCluster,
    DeHbLbCluster, DeHbJdCluster,
    DeLsAeCluster, DeLsBhCluster, DeLsEgCluster,
    DeLsKsCluster, DeLsKwCluster, DeLsOmCluster, DeLsQtCluster, DeLsLbCluster, DeLsJdCluster,
    DeSpAeCluster, DeSpBhCluster, DeSpEgCluster,
    DeSpKsCluster, DeSpKwCluster, DeSpOmCluster, DeSpQtCluster, DeSpLbCluster, DeSpJdCluster,
    DeSpEgCluster,DeHbAePreopt ,AppHbStrCluster,AppSpStrCluster,AppLsStrCluster,
    AppSpStrCluster,
    AppHbStrCluster
)

CLUSTER_MODEL_MAP = {
    # HB
    "de_hb_ae_cluster": DeHbAeCluster,
    "de_hb_bh_cluster": DeHbBhCluster,
    "de_hb_eg_cluster": DeHbEgCluster,
    "de_hb_ks_cluster": DeHbKsCluster,
    "de_hb_kw_cluster": DeHbKwCluster,
    "de_hb_om_cluster": DeHbOmCluster,
    "de_hb_qt_cluster": DeHbQtCluster,
    "de_hb_lb_cluster": DeHbLbCluster,
    "de_hb_jd_cluster": DeHbJdCluster,

    # LS
    "de_ls_ae_cluster": DeLsAeCluster,
    "de_ls_bh_cluster": DeLsBhCluster,
    "de_ls_eg_cluster": DeLsEgCluster,
    "de_ls_ks_cluster": DeLsKsCluster,
    "de_ls_kw_cluster": DeLsKwCluster,
    "de_ls_om_cluster": DeLsOmCluster,
    "de_ls_qt_cluster": DeLsQtCluster,
    "de_ls_lb_cluster": DeLsLbCluster,
    "de_ls_jd_cluster": DeLsJdCluster,

    # SP
    "de_sp_ae_cluster": DeSpAeCluster,
    "de_sp_bh_cluster": DeSpBhCluster,
    "de_sp_eg_cluster": DeSpEgCluster,
    "de_sp_ks_cluster": DeSpKsCluster,
    "de_sp_kw_cluster": DeSpKwCluster,
    "de_sp_om_cluster": DeSpOmCluster,
    "de_sp_qt_cluster": DeSpQtCluster,
    "de_sp_lb_cluster": DeSpLbCluster,
    "de_sp_jd_cluster": DeSpJdCluster,
}

STR_CLUSTER_MODEL_MAP = {
    "app_hb_str_cluster": AppHbStrCluster,
    "app_ls_str_cluster": AppLsStrCluster,
    "app_sp_str_cluster": AppSpStrCluster
}


APP_CLUSTER_MODEL_MAP = {
    "app_hb_str_cluster": AppHbStrCluster,
    "app_ls_str_cluster": AppLsStrCluster,
    "app_sp_str_cluster": AppSpStrCluster,
}

DE_PREOPTIMIZATION_MODEL_MAP = {
    "de_hb_ae_preopt": DeHbAePreopt,
}
APP_PREOPTIMIZATION_MODEL_MAP = {
    "app_hb_preopt": AppHbPreopt,
}